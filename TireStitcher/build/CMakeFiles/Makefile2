# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/ReifenScanner/TireStitcher

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/ReifenScanner/TireStitcher/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/stitch_tire.dir/all
all: CMakeFiles/extract_frames.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/stitch_tire.dir/clean
clean: CMakeFiles/extract_frames.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/stitch_tire.dir

# All Build rule for target.
CMakeFiles/stitch_tire.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25 "Built target stitch_tire"
.PHONY : CMakeFiles/stitch_tire.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/stitch_tire.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles 18
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/stitch_tire.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles 0
.PHONY : CMakeFiles/stitch_tire.dir/rule

# Convenience name for target.
stitch_tire: CMakeFiles/stitch_tire.dir/rule
.PHONY : stitch_tire

# clean rule for target.
CMakeFiles/stitch_tire.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/clean
.PHONY : CMakeFiles/stitch_tire.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/extract_frames.dir

# All Build rule for target.
CMakeFiles/extract_frames.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=1,2,3,4,5,6,7 "Built target extract_frames"
.PHONY : CMakeFiles/extract_frames.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/extract_frames.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/extract_frames.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles 0
.PHONY : CMakeFiles/extract_frames.dir/rule

# Convenience name for target.
extract_frames: CMakeFiles/extract_frames.dir/rule
.PHONY : extract_frames

# clean rule for target.
CMakeFiles/extract_frames.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/clean
.PHONY : CMakeFiles/extract_frames.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

