g++  -fopenmp CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.o CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.o CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.o CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.o CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.o CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.o -o /home/<USER>/Desktop/ReifenScanner/extract_frames  -Wl,-rpath,/usr/local/lib: /usr/local/lib/libopencv_gapi.so.4.12.0 /usr/local/lib/libopencv_highgui.so.4.12.0 /usr/local/lib/libopencv_ml.so.4.12.0 /usr/local/lib/libopencv_objdetect.so.4.12.0 /usr/local/lib/libopencv_photo.so.4.12.0 /usr/local/lib/libopencv_stitching.so.4.12.0 /usr/local/lib/libopencv_video.so.4.12.0 /usr/local/lib/libopencv_videoio.so.4.12.0 /usr/lib/gcc/x86_64-linux-gnu/13/libgomp.so /usr/lib/x86_64-linux-gnu/libpthread.a /usr/local/lib/libopencv_imgcodecs.so.4.12.0 /usr/local/lib/libopencv_dnn.so.4.12.0 /usr/local/lib/libopencv_calib3d.so.4.12.0 /usr/local/lib/libopencv_features2d.so.4.12.0 /usr/local/lib/libopencv_flann.so.4.12.0 /usr/local/lib/libopencv_imgproc.so.4.12.0 /usr/local/lib/libopencv_core.so.4.12.0 
