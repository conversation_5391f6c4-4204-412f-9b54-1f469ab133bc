# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/ReifenScanner/TireStitcher

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/ReifenScanner/TireStitcher/build

# Include any dependencies generated for this target.
include CMakeFiles/extract_frames.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/extract_frames.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/extract_frames.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/extract_frames.dir/flags.make

CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.o: CMakeFiles/extract_frames.dir/flags.make
CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/main.cpp
CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.o: CMakeFiles/extract_frames.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.o -MF CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.o.d -o CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/main.cpp

CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/main.cpp > CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.i

CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/main.cpp -o CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.s

CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.o: CMakeFiles/extract_frames.dir/flags.make
CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/ffmpeg_extractor.cpp
CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.o: CMakeFiles/extract_frames.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.o -MF CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.o.d -o CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/ffmpeg_extractor.cpp

CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/ffmpeg_extractor.cpp > CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.i

CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/ffmpeg_extractor.cpp -o CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.s

CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.o: CMakeFiles/extract_frames.dir/flags.make
CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/opencv_extractor.cpp
CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.o: CMakeFiles/extract_frames.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.o -MF CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.o.d -o CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/opencv_extractor.cpp

CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/opencv_extractor.cpp > CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.i

CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/opencv_extractor.cpp -o CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.s

CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.o: CMakeFiles/extract_frames.dir/flags.make
CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/rotation_analysis.cpp
CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.o: CMakeFiles/extract_frames.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.o -MF CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.o.d -o CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/rotation_analysis.cpp

CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/rotation_analysis.cpp > CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.i

CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/frame_extraction/rotation_analysis.cpp -o CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.s

CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.o: CMakeFiles/extract_frames.dir/flags.make
CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/fs_util.cpp
CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.o: CMakeFiles/extract_frames.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.o -MF CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.o.d -o CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/fs_util.cpp

CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/fs_util.cpp > CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.i

CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/fs_util.cpp -o CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.s

CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.o: CMakeFiles/extract_frames.dir/flags.make
CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/frame_extraction_utils.cpp
CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.o: CMakeFiles/extract_frames.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.o -MF CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.o.d -o CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/frame_extraction_utils.cpp

CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/frame_extraction_utils.cpp > CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.i

CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/frame_extraction_utils.cpp -o CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.s

# Object files for target extract_frames
extract_frames_OBJECTS = \
"CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.o" \
"CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.o" \
"CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.o" \
"CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.o" \
"CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.o" \
"CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.o"

# External object files for target extract_frames
extract_frames_EXTERNAL_OBJECTS =

/home/<USER>/Desktop/ReifenScanner/extract_frames: CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.o
/home/<USER>/Desktop/ReifenScanner/extract_frames: CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.o
/home/<USER>/Desktop/ReifenScanner/extract_frames: CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.o
/home/<USER>/Desktop/ReifenScanner/extract_frames: CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.o
/home/<USER>/Desktop/ReifenScanner/extract_frames: CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.o
/home/<USER>/Desktop/ReifenScanner/extract_frames: CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.o
/home/<USER>/Desktop/ReifenScanner/extract_frames: CMakeFiles/extract_frames.dir/build.make
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/local/lib/libopencv_gapi.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/local/lib/libopencv_highgui.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/local/lib/libopencv_ml.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/local/lib/libopencv_objdetect.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/local/lib/libopencv_photo.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/local/lib/libopencv_stitching.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/local/lib/libopencv_video.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/local/lib/libopencv_videoio.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/lib/gcc/x86_64-linux-gnu/13/libgomp.so
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/lib/x86_64-linux-gnu/libpthread.a
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/local/lib/libopencv_imgcodecs.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/local/lib/libopencv_dnn.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/local/lib/libopencv_calib3d.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/local/lib/libopencv_features2d.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/local/lib/libopencv_flann.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/local/lib/libopencv_imgproc.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/extract_frames: /usr/local/lib/libopencv_core.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/extract_frames: CMakeFiles/extract_frames.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX executable /home/<USER>/Desktop/ReifenScanner/extract_frames"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/extract_frames.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/extract_frames.dir/build: /home/<USER>/Desktop/ReifenScanner/extract_frames
.PHONY : CMakeFiles/extract_frames.dir/build

CMakeFiles/extract_frames.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/extract_frames.dir/cmake_clean.cmake
.PHONY : CMakeFiles/extract_frames.dir/clean

CMakeFiles/extract_frames.dir/depend:
	cd /home/<USER>/Desktop/ReifenScanner/TireStitcher/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/ReifenScanner/TireStitcher /home/<USER>/Desktop/ReifenScanner/TireStitcher /home/<USER>/Desktop/ReifenScanner/TireStitcher/build /home/<USER>/Desktop/ReifenScanner/TireStitcher/build /home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles/extract_frames.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/extract_frames.dir/depend

