# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/ReifenScanner/TireStitcher

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/ReifenScanner/TireStitcher/build

# Include any dependencies generated for this target.
include CMakeFiles/stitch_tire.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/stitch_tire.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/stitch_tire.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/stitch_tire.dir/flags.make

CMakeFiles/stitch_tire.dir/main.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/main.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/main.cpp
CMakeFiles/stitch_tire.dir/main.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/stitch_tire.dir/main.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/main.cpp.o -MF CMakeFiles/stitch_tire.dir/main.cpp.o.d -o CMakeFiles/stitch_tire.dir/main.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/main.cpp

CMakeFiles/stitch_tire.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/main.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/main.cpp > CMakeFiles/stitch_tire.dir/main.cpp.i

CMakeFiles/stitch_tire.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/main.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/main.cpp -o CMakeFiles/stitch_tire.dir/main.cpp.s

CMakeFiles/stitch_tire.dir/stitching/utils.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/utils.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/utils.cpp
CMakeFiles/stitch_tire.dir/stitching/utils.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/utils.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/utils.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/utils.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/utils.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/utils.cpp

CMakeFiles/stitch_tire.dir/stitching/utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/utils.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/utils.cpp > CMakeFiles/stitch_tire.dir/stitching/utils.cpp.i

CMakeFiles/stitch_tire.dir/stitching/utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/utils.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/utils.cpp -o CMakeFiles/stitch_tire.dir/stitching/utils.cpp.s

CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/image_loader.cpp
CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/image_loader.cpp

CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/image_loader.cpp > CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.i

CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/image_loader.cpp -o CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.s

CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/strip_extraction.cpp
CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/strip_extraction.cpp

CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/strip_extraction.cpp > CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.i

CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/strip_extraction.cpp -o CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.s

CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/movement_detection.cpp
CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/movement_detection.cpp

CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/movement_detection.cpp > CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.i

CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/movement_detection.cpp -o CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.s

CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/image_blending.cpp
CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/image_blending.cpp

CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/image_blending.cpp > CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.i

CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/image_blending.cpp -o CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.s

CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/panorama_processor.cpp
CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/panorama_processor.cpp

CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/panorama_processor.cpp > CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.i

CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/panorama_processor.cpp -o CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.s

CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/fs_util.cpp
CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/fs_util.cpp

CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/fs_util.cpp > CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.i

CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/fs_util.cpp -o CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.s

CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/frame_extraction_utils.cpp
CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/frame_extraction_utils.cpp

CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/frame_extraction_utils.cpp > CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.i

CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/frame_extraction_utils.cpp -o CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.s

CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/common/base_config.cpp
CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/common/base_config.cpp

CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/common/base_config.cpp > CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.i

CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/common/base_config.cpp -o CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.s

CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/common/stitching_factory.cpp
CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/common/stitching_factory.cpp

CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/common/stitching_factory.cpp > CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.i

CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/common/stitching_factory.cpp -o CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.s

CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_4k/movement_detection_4k.cpp
CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_4k/movement_detection_4k.cpp

CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_4k/movement_detection_4k.cpp > CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.i

CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_4k/movement_detection_4k.cpp -o CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.s

CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_4k/panorama_processor_4k.cpp
CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_4k/panorama_processor_4k.cpp

CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_4k/panorama_processor_4k.cpp > CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.i

CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_4k/panorama_processor_4k.cpp -o CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.s

CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_4k/image_blending_4k.cpp
CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_4k/image_blending_4k.cpp

CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_4k/image_blending_4k.cpp > CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.i

CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_4k/image_blending_4k.cpp -o CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.s

CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_8k/movement_detection_8k.cpp
CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_8k/movement_detection_8k.cpp

CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_8k/movement_detection_8k.cpp > CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.i

CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_8k/movement_detection_8k.cpp -o CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.s

CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_8k/panorama_processor_8k.cpp
CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_8k/panorama_processor_8k.cpp

CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_8k/panorama_processor_8k.cpp > CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.i

CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_8k/panorama_processor_8k.cpp -o CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.s

CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.o: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.o: /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_8k/image_blending_8k.cpp
CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.o: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.o"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.o -MF CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.o.d -o CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.o -c /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_8k/image_blending_8k.cpp

CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_8k/image_blending_8k.cpp > CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.i

CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/ReifenScanner/TireStitcher/stitching/stitching_8k/image_blending_8k.cpp -o CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.s

# Object files for target stitch_tire
stitch_tire_OBJECTS = \
"CMakeFiles/stitch_tire.dir/main.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/utils.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.o" \
"CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.o"

# External object files for target stitch_tire
stitch_tire_EXTERNAL_OBJECTS =

/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/main.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/utils.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.o
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/build.make
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/local/lib/libopencv_gapi.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/local/lib/libopencv_highgui.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/local/lib/libopencv_ml.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/local/lib/libopencv_objdetect.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/local/lib/libopencv_photo.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/local/lib/libopencv_stitching.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/local/lib/libopencv_video.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/local/lib/libopencv_videoio.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/lib/gcc/x86_64-linux-gnu/13/libgomp.so
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/lib/x86_64-linux-gnu/libpthread.a
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/local/lib/libopencv_imgcodecs.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/local/lib/libopencv_dnn.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/local/lib/libopencv_calib3d.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/local/lib/libopencv_features2d.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/local/lib/libopencv_flann.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/local/lib/libopencv_imgproc.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/stitch_tire: /usr/local/lib/libopencv_core.so.4.12.0
/home/<USER>/Desktop/ReifenScanner/stitch_tire: CMakeFiles/stitch_tire.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Linking CXX executable /home/<USER>/Desktop/ReifenScanner/stitch_tire"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/stitch_tire.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/stitch_tire.dir/build: /home/<USER>/Desktop/ReifenScanner/stitch_tire
.PHONY : CMakeFiles/stitch_tire.dir/build

CMakeFiles/stitch_tire.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/stitch_tire.dir/cmake_clean.cmake
.PHONY : CMakeFiles/stitch_tire.dir/clean

CMakeFiles/stitch_tire.dir/depend:
	cd /home/<USER>/Desktop/ReifenScanner/TireStitcher/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/ReifenScanner/TireStitcher /home/<USER>/Desktop/ReifenScanner/TireStitcher /home/<USER>/Desktop/ReifenScanner/TireStitcher/build /home/<USER>/Desktop/ReifenScanner/TireStitcher/build /home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles/stitch_tire.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/stitch_tire.dir/depend

