# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/ReifenScanner/TireStitcher

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/ReifenScanner/TireStitcher/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles /home/<USER>/Desktop/ReifenScanner/TireStitcher/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Desktop/ReifenScanner/TireStitcher/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named stitch_tire

# Build rule for target.
stitch_tire: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 stitch_tire
.PHONY : stitch_tire

# fast build rule for target.
stitch_tire/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/build
.PHONY : stitch_tire/fast

#=============================================================================
# Target rules for targets named extract_frames

# Build rule for target.
extract_frames: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 extract_frames
.PHONY : extract_frames

# fast build rule for target.
extract_frames/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/build
.PHONY : extract_frames/fast

frame_extraction/ffmpeg_extractor.o: frame_extraction/ffmpeg_extractor.cpp.o
.PHONY : frame_extraction/ffmpeg_extractor.o

# target to build an object file
frame_extraction/ffmpeg_extractor.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.o
.PHONY : frame_extraction/ffmpeg_extractor.cpp.o

frame_extraction/ffmpeg_extractor.i: frame_extraction/ffmpeg_extractor.cpp.i
.PHONY : frame_extraction/ffmpeg_extractor.i

# target to preprocess a source file
frame_extraction/ffmpeg_extractor.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.i
.PHONY : frame_extraction/ffmpeg_extractor.cpp.i

frame_extraction/ffmpeg_extractor.s: frame_extraction/ffmpeg_extractor.cpp.s
.PHONY : frame_extraction/ffmpeg_extractor.s

# target to generate assembly for a file
frame_extraction/ffmpeg_extractor.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.s
.PHONY : frame_extraction/ffmpeg_extractor.cpp.s

frame_extraction/main.o: frame_extraction/main.cpp.o
.PHONY : frame_extraction/main.o

# target to build an object file
frame_extraction/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.o
.PHONY : frame_extraction/main.cpp.o

frame_extraction/main.i: frame_extraction/main.cpp.i
.PHONY : frame_extraction/main.i

# target to preprocess a source file
frame_extraction/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.i
.PHONY : frame_extraction/main.cpp.i

frame_extraction/main.s: frame_extraction/main.cpp.s
.PHONY : frame_extraction/main.s

# target to generate assembly for a file
frame_extraction/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.s
.PHONY : frame_extraction/main.cpp.s

frame_extraction/opencv_extractor.o: frame_extraction/opencv_extractor.cpp.o
.PHONY : frame_extraction/opencv_extractor.o

# target to build an object file
frame_extraction/opencv_extractor.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.o
.PHONY : frame_extraction/opencv_extractor.cpp.o

frame_extraction/opencv_extractor.i: frame_extraction/opencv_extractor.cpp.i
.PHONY : frame_extraction/opencv_extractor.i

# target to preprocess a source file
frame_extraction/opencv_extractor.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.i
.PHONY : frame_extraction/opencv_extractor.cpp.i

frame_extraction/opencv_extractor.s: frame_extraction/opencv_extractor.cpp.s
.PHONY : frame_extraction/opencv_extractor.s

# target to generate assembly for a file
frame_extraction/opencv_extractor.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.s
.PHONY : frame_extraction/opencv_extractor.cpp.s

frame_extraction/rotation_analysis.o: frame_extraction/rotation_analysis.cpp.o
.PHONY : frame_extraction/rotation_analysis.o

# target to build an object file
frame_extraction/rotation_analysis.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.o
.PHONY : frame_extraction/rotation_analysis.cpp.o

frame_extraction/rotation_analysis.i: frame_extraction/rotation_analysis.cpp.i
.PHONY : frame_extraction/rotation_analysis.i

# target to preprocess a source file
frame_extraction/rotation_analysis.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.i
.PHONY : frame_extraction/rotation_analysis.cpp.i

frame_extraction/rotation_analysis.s: frame_extraction/rotation_analysis.cpp.s
.PHONY : frame_extraction/rotation_analysis.s

# target to generate assembly for a file
frame_extraction/rotation_analysis.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.s
.PHONY : frame_extraction/rotation_analysis.cpp.s

main.o: main.cpp.o
.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/main.cpp.s
.PHONY : main.cpp.s

stitching/common/base_config.o: stitching/common/base_config.cpp.o
.PHONY : stitching/common/base_config.o

# target to build an object file
stitching/common/base_config.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.o
.PHONY : stitching/common/base_config.cpp.o

stitching/common/base_config.i: stitching/common/base_config.cpp.i
.PHONY : stitching/common/base_config.i

# target to preprocess a source file
stitching/common/base_config.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.i
.PHONY : stitching/common/base_config.cpp.i

stitching/common/base_config.s: stitching/common/base_config.cpp.s
.PHONY : stitching/common/base_config.s

# target to generate assembly for a file
stitching/common/base_config.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/common/base_config.cpp.s
.PHONY : stitching/common/base_config.cpp.s

stitching/common/stitching_factory.o: stitching/common/stitching_factory.cpp.o
.PHONY : stitching/common/stitching_factory.o

# target to build an object file
stitching/common/stitching_factory.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.o
.PHONY : stitching/common/stitching_factory.cpp.o

stitching/common/stitching_factory.i: stitching/common/stitching_factory.cpp.i
.PHONY : stitching/common/stitching_factory.i

# target to preprocess a source file
stitching/common/stitching_factory.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.i
.PHONY : stitching/common/stitching_factory.cpp.i

stitching/common/stitching_factory.s: stitching/common/stitching_factory.cpp.s
.PHONY : stitching/common/stitching_factory.s

# target to generate assembly for a file
stitching/common/stitching_factory.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/common/stitching_factory.cpp.s
.PHONY : stitching/common/stitching_factory.cpp.s

stitching/frame_extraction_utils.o: stitching/frame_extraction_utils.cpp.o
.PHONY : stitching/frame_extraction_utils.o

# target to build an object file
stitching/frame_extraction_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.o
.PHONY : stitching/frame_extraction_utils.cpp.o

stitching/frame_extraction_utils.i: stitching/frame_extraction_utils.cpp.i
.PHONY : stitching/frame_extraction_utils.i

# target to preprocess a source file
stitching/frame_extraction_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.i
.PHONY : stitching/frame_extraction_utils.cpp.i

stitching/frame_extraction_utils.s: stitching/frame_extraction_utils.cpp.s
.PHONY : stitching/frame_extraction_utils.s

# target to generate assembly for a file
stitching/frame_extraction_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.s
.PHONY : stitching/frame_extraction_utils.cpp.s

stitching/fs_util.o: stitching/fs_util.cpp.o
.PHONY : stitching/fs_util.o

# target to build an object file
stitching/fs_util.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.o
.PHONY : stitching/fs_util.cpp.o

stitching/fs_util.i: stitching/fs_util.cpp.i
.PHONY : stitching/fs_util.i

# target to preprocess a source file
stitching/fs_util.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.i
.PHONY : stitching/fs_util.cpp.i

stitching/fs_util.s: stitching/fs_util.cpp.s
.PHONY : stitching/fs_util.s

# target to generate assembly for a file
stitching/fs_util.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/extract_frames.dir/build.make CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.s
.PHONY : stitching/fs_util.cpp.s

stitching/image_blending.o: stitching/image_blending.cpp.o
.PHONY : stitching/image_blending.o

# target to build an object file
stitching/image_blending.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.o
.PHONY : stitching/image_blending.cpp.o

stitching/image_blending.i: stitching/image_blending.cpp.i
.PHONY : stitching/image_blending.i

# target to preprocess a source file
stitching/image_blending.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.i
.PHONY : stitching/image_blending.cpp.i

stitching/image_blending.s: stitching/image_blending.cpp.s
.PHONY : stitching/image_blending.s

# target to generate assembly for a file
stitching/image_blending.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.s
.PHONY : stitching/image_blending.cpp.s

stitching/image_loader.o: stitching/image_loader.cpp.o
.PHONY : stitching/image_loader.o

# target to build an object file
stitching/image_loader.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.o
.PHONY : stitching/image_loader.cpp.o

stitching/image_loader.i: stitching/image_loader.cpp.i
.PHONY : stitching/image_loader.i

# target to preprocess a source file
stitching/image_loader.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.i
.PHONY : stitching/image_loader.cpp.i

stitching/image_loader.s: stitching/image_loader.cpp.s
.PHONY : stitching/image_loader.s

# target to generate assembly for a file
stitching/image_loader.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.s
.PHONY : stitching/image_loader.cpp.s

stitching/movement_detection.o: stitching/movement_detection.cpp.o
.PHONY : stitching/movement_detection.o

# target to build an object file
stitching/movement_detection.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.o
.PHONY : stitching/movement_detection.cpp.o

stitching/movement_detection.i: stitching/movement_detection.cpp.i
.PHONY : stitching/movement_detection.i

# target to preprocess a source file
stitching/movement_detection.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.i
.PHONY : stitching/movement_detection.cpp.i

stitching/movement_detection.s: stitching/movement_detection.cpp.s
.PHONY : stitching/movement_detection.s

# target to generate assembly for a file
stitching/movement_detection.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.s
.PHONY : stitching/movement_detection.cpp.s

stitching/panorama_processor.o: stitching/panorama_processor.cpp.o
.PHONY : stitching/panorama_processor.o

# target to build an object file
stitching/panorama_processor.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.o
.PHONY : stitching/panorama_processor.cpp.o

stitching/panorama_processor.i: stitching/panorama_processor.cpp.i
.PHONY : stitching/panorama_processor.i

# target to preprocess a source file
stitching/panorama_processor.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.i
.PHONY : stitching/panorama_processor.cpp.i

stitching/panorama_processor.s: stitching/panorama_processor.cpp.s
.PHONY : stitching/panorama_processor.s

# target to generate assembly for a file
stitching/panorama_processor.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.s
.PHONY : stitching/panorama_processor.cpp.s

stitching/stitching_4k/image_blending_4k.o: stitching/stitching_4k/image_blending_4k.cpp.o
.PHONY : stitching/stitching_4k/image_blending_4k.o

# target to build an object file
stitching/stitching_4k/image_blending_4k.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.o
.PHONY : stitching/stitching_4k/image_blending_4k.cpp.o

stitching/stitching_4k/image_blending_4k.i: stitching/stitching_4k/image_blending_4k.cpp.i
.PHONY : stitching/stitching_4k/image_blending_4k.i

# target to preprocess a source file
stitching/stitching_4k/image_blending_4k.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.i
.PHONY : stitching/stitching_4k/image_blending_4k.cpp.i

stitching/stitching_4k/image_blending_4k.s: stitching/stitching_4k/image_blending_4k.cpp.s
.PHONY : stitching/stitching_4k/image_blending_4k.s

# target to generate assembly for a file
stitching/stitching_4k/image_blending_4k.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_4k/image_blending_4k.cpp.s
.PHONY : stitching/stitching_4k/image_blending_4k.cpp.s

stitching/stitching_4k/movement_detection_4k.o: stitching/stitching_4k/movement_detection_4k.cpp.o
.PHONY : stitching/stitching_4k/movement_detection_4k.o

# target to build an object file
stitching/stitching_4k/movement_detection_4k.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.o
.PHONY : stitching/stitching_4k/movement_detection_4k.cpp.o

stitching/stitching_4k/movement_detection_4k.i: stitching/stitching_4k/movement_detection_4k.cpp.i
.PHONY : stitching/stitching_4k/movement_detection_4k.i

# target to preprocess a source file
stitching/stitching_4k/movement_detection_4k.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.i
.PHONY : stitching/stitching_4k/movement_detection_4k.cpp.i

stitching/stitching_4k/movement_detection_4k.s: stitching/stitching_4k/movement_detection_4k.cpp.s
.PHONY : stitching/stitching_4k/movement_detection_4k.s

# target to generate assembly for a file
stitching/stitching_4k/movement_detection_4k.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_4k/movement_detection_4k.cpp.s
.PHONY : stitching/stitching_4k/movement_detection_4k.cpp.s

stitching/stitching_4k/panorama_processor_4k.o: stitching/stitching_4k/panorama_processor_4k.cpp.o
.PHONY : stitching/stitching_4k/panorama_processor_4k.o

# target to build an object file
stitching/stitching_4k/panorama_processor_4k.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.o
.PHONY : stitching/stitching_4k/panorama_processor_4k.cpp.o

stitching/stitching_4k/panorama_processor_4k.i: stitching/stitching_4k/panorama_processor_4k.cpp.i
.PHONY : stitching/stitching_4k/panorama_processor_4k.i

# target to preprocess a source file
stitching/stitching_4k/panorama_processor_4k.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.i
.PHONY : stitching/stitching_4k/panorama_processor_4k.cpp.i

stitching/stitching_4k/panorama_processor_4k.s: stitching/stitching_4k/panorama_processor_4k.cpp.s
.PHONY : stitching/stitching_4k/panorama_processor_4k.s

# target to generate assembly for a file
stitching/stitching_4k/panorama_processor_4k.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_4k/panorama_processor_4k.cpp.s
.PHONY : stitching/stitching_4k/panorama_processor_4k.cpp.s

stitching/stitching_8k/image_blending_8k.o: stitching/stitching_8k/image_blending_8k.cpp.o
.PHONY : stitching/stitching_8k/image_blending_8k.o

# target to build an object file
stitching/stitching_8k/image_blending_8k.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.o
.PHONY : stitching/stitching_8k/image_blending_8k.cpp.o

stitching/stitching_8k/image_blending_8k.i: stitching/stitching_8k/image_blending_8k.cpp.i
.PHONY : stitching/stitching_8k/image_blending_8k.i

# target to preprocess a source file
stitching/stitching_8k/image_blending_8k.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.i
.PHONY : stitching/stitching_8k/image_blending_8k.cpp.i

stitching/stitching_8k/image_blending_8k.s: stitching/stitching_8k/image_blending_8k.cpp.s
.PHONY : stitching/stitching_8k/image_blending_8k.s

# target to generate assembly for a file
stitching/stitching_8k/image_blending_8k.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_8k/image_blending_8k.cpp.s
.PHONY : stitching/stitching_8k/image_blending_8k.cpp.s

stitching/stitching_8k/movement_detection_8k.o: stitching/stitching_8k/movement_detection_8k.cpp.o
.PHONY : stitching/stitching_8k/movement_detection_8k.o

# target to build an object file
stitching/stitching_8k/movement_detection_8k.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.o
.PHONY : stitching/stitching_8k/movement_detection_8k.cpp.o

stitching/stitching_8k/movement_detection_8k.i: stitching/stitching_8k/movement_detection_8k.cpp.i
.PHONY : stitching/stitching_8k/movement_detection_8k.i

# target to preprocess a source file
stitching/stitching_8k/movement_detection_8k.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.i
.PHONY : stitching/stitching_8k/movement_detection_8k.cpp.i

stitching/stitching_8k/movement_detection_8k.s: stitching/stitching_8k/movement_detection_8k.cpp.s
.PHONY : stitching/stitching_8k/movement_detection_8k.s

# target to generate assembly for a file
stitching/stitching_8k/movement_detection_8k.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_8k/movement_detection_8k.cpp.s
.PHONY : stitching/stitching_8k/movement_detection_8k.cpp.s

stitching/stitching_8k/panorama_processor_8k.o: stitching/stitching_8k/panorama_processor_8k.cpp.o
.PHONY : stitching/stitching_8k/panorama_processor_8k.o

# target to build an object file
stitching/stitching_8k/panorama_processor_8k.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.o
.PHONY : stitching/stitching_8k/panorama_processor_8k.cpp.o

stitching/stitching_8k/panorama_processor_8k.i: stitching/stitching_8k/panorama_processor_8k.cpp.i
.PHONY : stitching/stitching_8k/panorama_processor_8k.i

# target to preprocess a source file
stitching/stitching_8k/panorama_processor_8k.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.i
.PHONY : stitching/stitching_8k/panorama_processor_8k.cpp.i

stitching/stitching_8k/panorama_processor_8k.s: stitching/stitching_8k/panorama_processor_8k.cpp.s
.PHONY : stitching/stitching_8k/panorama_processor_8k.s

# target to generate assembly for a file
stitching/stitching_8k/panorama_processor_8k.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/stitching_8k/panorama_processor_8k.cpp.s
.PHONY : stitching/stitching_8k/panorama_processor_8k.cpp.s

stitching/strip_extraction.o: stitching/strip_extraction.cpp.o
.PHONY : stitching/strip_extraction.o

# target to build an object file
stitching/strip_extraction.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.o
.PHONY : stitching/strip_extraction.cpp.o

stitching/strip_extraction.i: stitching/strip_extraction.cpp.i
.PHONY : stitching/strip_extraction.i

# target to preprocess a source file
stitching/strip_extraction.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.i
.PHONY : stitching/strip_extraction.cpp.i

stitching/strip_extraction.s: stitching/strip_extraction.cpp.s
.PHONY : stitching/strip_extraction.s

# target to generate assembly for a file
stitching/strip_extraction.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.s
.PHONY : stitching/strip_extraction.cpp.s

stitching/utils.o: stitching/utils.cpp.o
.PHONY : stitching/utils.o

# target to build an object file
stitching/utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/utils.cpp.o
.PHONY : stitching/utils.cpp.o

stitching/utils.i: stitching/utils.cpp.i
.PHONY : stitching/utils.i

# target to preprocess a source file
stitching/utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/utils.cpp.i
.PHONY : stitching/utils.cpp.i

stitching/utils.s: stitching/utils.cpp.s
.PHONY : stitching/utils.s

# target to generate assembly for a file
stitching/utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stitch_tire.dir/build.make CMakeFiles/stitch_tire.dir/stitching/utils.cpp.s
.PHONY : stitching/utils.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... extract_frames"
	@echo "... stitch_tire"
	@echo "... frame_extraction/ffmpeg_extractor.o"
	@echo "... frame_extraction/ffmpeg_extractor.i"
	@echo "... frame_extraction/ffmpeg_extractor.s"
	@echo "... frame_extraction/main.o"
	@echo "... frame_extraction/main.i"
	@echo "... frame_extraction/main.s"
	@echo "... frame_extraction/opencv_extractor.o"
	@echo "... frame_extraction/opencv_extractor.i"
	@echo "... frame_extraction/opencv_extractor.s"
	@echo "... frame_extraction/rotation_analysis.o"
	@echo "... frame_extraction/rotation_analysis.i"
	@echo "... frame_extraction/rotation_analysis.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... stitching/common/base_config.o"
	@echo "... stitching/common/base_config.i"
	@echo "... stitching/common/base_config.s"
	@echo "... stitching/common/stitching_factory.o"
	@echo "... stitching/common/stitching_factory.i"
	@echo "... stitching/common/stitching_factory.s"
	@echo "... stitching/frame_extraction_utils.o"
	@echo "... stitching/frame_extraction_utils.i"
	@echo "... stitching/frame_extraction_utils.s"
	@echo "... stitching/fs_util.o"
	@echo "... stitching/fs_util.i"
	@echo "... stitching/fs_util.s"
	@echo "... stitching/image_blending.o"
	@echo "... stitching/image_blending.i"
	@echo "... stitching/image_blending.s"
	@echo "... stitching/image_loader.o"
	@echo "... stitching/image_loader.i"
	@echo "... stitching/image_loader.s"
	@echo "... stitching/movement_detection.o"
	@echo "... stitching/movement_detection.i"
	@echo "... stitching/movement_detection.s"
	@echo "... stitching/panorama_processor.o"
	@echo "... stitching/panorama_processor.i"
	@echo "... stitching/panorama_processor.s"
	@echo "... stitching/stitching_4k/image_blending_4k.o"
	@echo "... stitching/stitching_4k/image_blending_4k.i"
	@echo "... stitching/stitching_4k/image_blending_4k.s"
	@echo "... stitching/stitching_4k/movement_detection_4k.o"
	@echo "... stitching/stitching_4k/movement_detection_4k.i"
	@echo "... stitching/stitching_4k/movement_detection_4k.s"
	@echo "... stitching/stitching_4k/panorama_processor_4k.o"
	@echo "... stitching/stitching_4k/panorama_processor_4k.i"
	@echo "... stitching/stitching_4k/panorama_processor_4k.s"
	@echo "... stitching/stitching_8k/image_blending_8k.o"
	@echo "... stitching/stitching_8k/image_blending_8k.i"
	@echo "... stitching/stitching_8k/image_blending_8k.s"
	@echo "... stitching/stitching_8k/movement_detection_8k.o"
	@echo "... stitching/stitching_8k/movement_detection_8k.i"
	@echo "... stitching/stitching_8k/movement_detection_8k.s"
	@echo "... stitching/stitching_8k/panorama_processor_8k.o"
	@echo "... stitching/stitching_8k/panorama_processor_8k.i"
	@echo "... stitching/stitching_8k/panorama_processor_8k.s"
	@echo "... stitching/strip_extraction.o"
	@echo "... stitching/strip_extraction.i"
	@echo "... stitching/strip_extraction.s"
	@echo "... stitching/utils.o"
	@echo "... stitching/utils.i"
	@echo "... stitching/utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

