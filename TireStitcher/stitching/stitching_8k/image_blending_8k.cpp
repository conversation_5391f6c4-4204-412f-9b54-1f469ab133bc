#include "image_blending_8k.h"
#include "../debug_utils.h"
#include <chrono>
#include <algorithm>
#include <cmath>
#include <opencv2/imgproc.hpp>
#include <opencv2/photo.hpp>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace Stitching8K {

cv::Mat blendImages(const cv::Mat& img1, const cv::Mat& img2, int overlapWidth, const StitchConfig8K& config) {
    int h1 = img1.rows;
    int w1 = img1.cols;
    int h2 = img2.rows;
    int w2 = img2.cols;

    // Ensure consistent height with high-quality resizing
    cv::Mat resizedImg2;
    if (h1 != h2) {
        cv::resize(img2, resizedImg2, cv::Size(w2, h1), 0, 0, cv::INTER_CUBIC);
    } else {
        resizedImg2 = img2;
    }

    // Apply color correction for 8K quality
    cv::Mat correctedImg1 = img1.clone();
    cv::Mat correctedImg2 = resizedImg2.clone();
    applyColorCorrection8K(correctedImg1, correctedImg2, config);

    // The output image will have width of both images minus the overlap
    int outputWidth = w1 + w2 - overlapWidth;
    cv::Mat result(h1, outputWidth, img1.type(), cv::Scalar(0, 0, 0));

    // Copy the non-overlapping part of the first image
    correctedImg1(cv::Rect(0, 0, w1 - overlapWidth, h1)).copyTo(
        result(cv::Rect(0, 0, w1 - overlapWidth, h1)));

    // Enhanced blending for 8K quality
    if (config.useEnhancedBlending) {
        cv::Mat mask = createFeatheredMask8K(overlapWidth, h1, config.blendingFeatherPercent, config);

        // Extract overlapping regions
        cv::Mat overlap1 = correctedImg1(cv::Rect(w1 - overlapWidth, 0, overlapWidth, h1));
        cv::Mat overlap2 = correctedImg2(cv::Rect(0, 0, overlapWidth, h1));

        // Multi-channel blending with feathering
        cv::Mat blendedOverlap;
        std::vector<cv::Mat> channels1, channels2, blendedChannels;
        cv::split(overlap1, channels1);
        cv::split(overlap2, channels2);

        for (size_t i = 0; i < channels1.size(); i++) {
            cv::Mat blendedChannel;
            cv::addWeighted(channels1[i], 0.5, channels2[i], 0.5, 0, blendedChannel);

            // Apply feathered mask
            cv::Mat maskedChannel1, maskedChannel2;
            channels1[i].convertTo(maskedChannel1, CV_32F);
            channels2[i].convertTo(maskedChannel2, CV_32F);

            cv::Mat mask32f;
            mask.convertTo(mask32f, CV_32F);

            cv::Mat finalChannel = maskedChannel1.mul(mask32f) + maskedChannel2.mul(1.0 - mask32f);
            finalChannel.convertTo(finalChannel, channels1[i].type());
            blendedChannels.push_back(finalChannel);
        }

        cv::merge(blendedChannels, blendedOverlap);

        // Copy the blended area
        blendedOverlap.copyTo(result(cv::Rect(w1 - overlapWidth, 0, overlapWidth, h1)));
    } else {
        // Simple linear blending
        cv::Mat mask = getBlendingMask8K(overlapWidth, h1, config);

        cv::Mat overlap1 = correctedImg1(cv::Rect(w1 - overlapWidth, 0, overlapWidth, h1));
        cv::Mat overlap2 = correctedImg2(cv::Rect(0, 0, overlapWidth, h1));

        cv::Mat blendedOverlap;
        cv::addWeighted(overlap1, 0.5, overlap2, 0.5, 0, blendedOverlap);

        blendedOverlap.copyTo(result(cv::Rect(w1 - overlapWidth, 0, overlapWidth, h1)));
    }

    // Copy the non-overlapping part of the second image
    correctedImg2(cv::Rect(overlapWidth, 0, w2 - overlapWidth, h1)).copyTo(
        result(cv::Rect(w1, 0, w2 - overlapWidth, h1)));

    return result;
}

cv::Mat getBlendingMask8K(int width, int height, const StitchConfig8K& config) {
    // Enhanced gradient mask for 8K quality
    cv::Mat mask(height, width, CV_32F);

    for (int i = 0; i < width; i++) {
        // Smooth sigmoid-like transition for better blending
        float t = static_cast<float>(i) / (width - 1);
        float value = 1.0f - (0.5f * (1.0f + std::tanh(4.0f * (t - 0.5f))));
        mask.col(i).setTo(value);
    }

    return mask;
}

int calculateOptimalOverlap8K(const cv::Mat& img1, const cv::Mat& img2, double correlation,
                             const StitchConfig8K& config) {
    // Advanced overlap calculation for 8K quality with reduced minimum overlap
    int baseOverlap = std::max(config.minOverlap, img1.cols / static_cast<int>(config.overlapScaleFactor));

    // Adjust based on correlation with 8K quality thresholds
    if (correlation >= config.correlationThresholdHigh) {
        // High correlation - use reduced overlap for quality
        baseOverlap = std::max(config.minOverlap, baseOverlap / 3);
        safePrint("8K: High correlation - reduced overlap: " + std::to_string(baseOverlap), config.verbose);
    } else if (correlation >= config.correlationThresholdLow) {
        // Medium correlation - moderate overlap
        baseOverlap = std::max(config.minOverlap, static_cast<int>(baseOverlap * 0.7));
        safePrint("8K: Medium correlation - moderate overlap: " + std::to_string(baseOverlap), config.verbose);
    }

    return std::min(baseOverlap, config.maxOverlap);
}

cv::Mat enhancePanorama8K(const cv::Mat& panorama, const std::string& outputPath, const StitchConfig8K& config) {
    safePrint("8K: Creating enhanced panorama with comprehensive processing...", true);
    auto startTime = std::chrono::high_resolution_clock::now();

    cv::Mat enhanced = panorama.clone();

    // Apply CLAHE for contrast enhancement
    applyCLAHE8K(enhanced, config);

    // Apply unsharp masking for sharpness enhancement
    applyUnsharpMask8K(enhanced, config);

    // Advanced color enhancement
    cv::Mat lab;
    cv::cvtColor(enhanced, lab, cv::COLOR_BGR2Lab);

    std::vector<cv::Mat> labChannels;
    cv::split(lab, labChannels);

    // Enhance L channel (lightness)
    labChannels[0].convertTo(labChannels[0], -1, 1.1, 5);  // Slight contrast and brightness boost

    // Enhance A and B channels (color)
    labChannels[1].convertTo(labChannels[1], -1, 1.05, 0);  // Slight color enhancement
    labChannels[2].convertTo(labChannels[2], -1, 1.05, 0);

    cv::merge(labChannels, lab);
    cv::cvtColor(lab, enhanced, cv::COLOR_Lab2BGR);

    // Noise reduction for 8K quality
    cv::Mat denoised;
    cv::fastNlMeansDenoisingColored(enhanced, denoised, 3, 3, 7, 21);
    enhanced = denoised;

    // Save the enhanced image with maximum quality
    std::vector<int> jpegParams = {cv::IMWRITE_JPEG_QUALITY, config.jpegQuality};
    cv::imwrite(outputPath, enhanced, jpegParams);

    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

    safePrint("8K: Enhanced panorama saved to: " + outputPath, true);
    safePrint("8K: Enhancement time: " + std::to_string(duration) + " ms", true);

    return enhanced;
}

cv::Mat blendStrips8K(const cv::Mat& panorama, const cv::Mat& strip, int overlap, const StitchConfig8K& config) {
    return blendImages(panorama, strip, overlap, config);
}

cv::Mat multiBandBlend8K(const cv::Mat& img1, const cv::Mat& img2, int overlap, const StitchConfig8K& config) {
    // Advanced multi-band blending for highest 8K quality
    safePrint("8K: Applying multi-band blending for maximum quality", config.verbose);

    int h1 = img1.rows;
    int w1 = img1.cols;
    int h2 = img2.rows;
    int w2 = img2.cols;

    // Ensure consistent height
    cv::Mat resizedImg2;
    if (h1 != h2) {
        cv::resize(img2, resizedImg2, cv::Size(w2, h1), 0, 0, cv::INTER_CUBIC);
    } else {
        resizedImg2 = img2;
    }

    // Create Laplacian pyramids
    const int levels = 4;  // Number of pyramid levels
    std::vector<cv::Mat> pyramid1, pyramid2;

    // Build pyramids for overlapping regions
    cv::Mat overlap1 = img1(cv::Rect(w1 - overlap, 0, overlap, h1));
    cv::Mat overlap2 = resizedImg2(cv::Rect(0, 0, overlap, h1));

    // Build Gaussian pyramids first
    std::vector<cv::Mat> gauss1, gauss2;
    gauss1.push_back(overlap1);
    gauss2.push_back(overlap2);

    for (int i = 0; i < levels - 1; i++) {
        cv::Mat down1, down2;
        cv::pyrDown(gauss1[i], down1);
        cv::pyrDown(gauss2[i], down2);
        gauss1.push_back(down1);
        gauss2.push_back(down2);
    }

    // Build Laplacian pyramids
    for (int i = 0; i < levels - 1; i++) {
        cv::Mat up1, up2, lap1, lap2;
        cv::pyrUp(gauss1[i + 1], up1, gauss1[i].size());
        cv::pyrUp(gauss2[i + 1], up2, gauss2[i].size());
        cv::subtract(gauss1[i], up1, lap1);
        cv::subtract(gauss2[i], up2, lap2);
        pyramid1.push_back(lap1);
        pyramid2.push_back(lap2);
    }
    pyramid1.push_back(gauss1[levels - 1]);
    pyramid2.push_back(gauss2[levels - 1]);

    // Blend pyramids
    std::vector<cv::Mat> blendedPyramid;
    for (int i = 0; i < levels; i++) {
        cv::Mat blended;
        cv::addWeighted(pyramid1[i], 0.5, pyramid2[i], 0.5, 0, blended);
        blendedPyramid.push_back(blended);
    }

    // Reconstruct from pyramid
    cv::Mat result = blendedPyramid[levels - 1];
    for (int i = levels - 2; i >= 0; i--) {
        cv::Mat up;
        cv::pyrUp(result, up, blendedPyramid[i].size());
        cv::add(up, blendedPyramid[i], result);
    }

    // Combine with full images
    int outputWidth = w1 + w2 - overlap;
    cv::Mat finalResult(h1, outputWidth, img1.type());

    // Copy non-overlapping parts
    img1(cv::Rect(0, 0, w1 - overlap, h1)).copyTo(
        finalResult(cv::Rect(0, 0, w1 - overlap, h1)));

    result.copyTo(finalResult(cv::Rect(w1 - overlap, 0, overlap, h1)));

    resizedImg2(cv::Rect(overlap, 0, w2 - overlap, h1)).copyTo(
        finalResult(cv::Rect(w1, 0, w2 - overlap, h1)));

    return finalResult;
}

void applyColorCorrection8K(cv::Mat& img1, cv::Mat& img2, const StitchConfig8K& config) {
    // Advanced color correction for 8K quality
    safePrint("8K: Applying advanced color correction", config.verbose);

    // Convert to Lab color space for better color matching
    cv::Mat lab1, lab2;
    cv::cvtColor(img1, lab1, cv::COLOR_BGR2Lab);
    cv::cvtColor(img2, lab2, cv::COLOR_BGR2Lab);

    // Calculate mean and standard deviation for each channel
    cv::Scalar mean1, stddev1, mean2, stddev2;
    cv::meanStdDev(lab1, mean1, stddev1);
    cv::meanStdDev(lab2, mean2, stddev2);

    // Match statistics for L, A, B channels
    std::vector<cv::Mat> channels1, channels2;
    cv::split(lab1, channels1);
    cv::split(lab2, channels2);

    for (int i = 0; i < 3; i++) {
        if (stddev2[i] > 0) {
            double scale = stddev1[i] / stddev2[i];
            double shift = mean1[i] - mean2[i] * scale;

            channels2[i].convertTo(channels2[i], -1, scale, shift);
        }
    }

    cv::merge(channels2, lab2);
    cv::cvtColor(lab2, img2, cv::COLOR_Lab2BGR);

    safePrint("8K: Color correction applied", config.verbose);
}

cv::Mat createFeatheredMask8K(int width, int height, double featherPercent, const StitchConfig8K& config) {
    cv::Mat mask(height, width, CV_32F);

    int featherWidth = static_cast<int>(width * featherPercent);

    for (int i = 0; i < width; i++) {
        float value;
        if (i < featherWidth) {
            // Smooth transition at the beginning
            float t = static_cast<float>(i) / featherWidth;
            value = 1.0f - 0.5f * (1.0f + std::cos(M_PI * t));
        } else if (i > width - featherWidth) {
            // Smooth transition at the end
            float t = static_cast<float>(width - i) / featherWidth;
            value = 0.5f * (1.0f + std::cos(M_PI * t));
        } else {
            // Linear transition in the middle
            float t = static_cast<float>(i - featherWidth) / (width - 2 * featherWidth);
            value = 1.0f - t;
        }

        mask.col(i).setTo(value);
    }

    return mask;
}

void applyCLAHE8K(cv::Mat& image, const StitchConfig8K& config) {
    // Apply CLAHE (Contrast Limited Adaptive Histogram Equalization) for 8K quality
    cv::Mat lab;
    cv::cvtColor(image, lab, cv::COLOR_BGR2Lab);

    std::vector<cv::Mat> labChannels;
    cv::split(lab, labChannels);

    // Apply CLAHE to L channel
    cv::Ptr<cv::CLAHE> clahe = cv::createCLAHE(config.claheClipLimit, cv::Size(config.claheTileSize, config.claheTileSize));
    clahe->apply(labChannels[0], labChannels[0]);

    cv::merge(labChannels, lab);
    cv::cvtColor(lab, image, cv::COLOR_Lab2BGR);

    safePrint("8K: CLAHE enhancement applied", config.verbose);
}

void applyUnsharpMask8K(cv::Mat& image, const StitchConfig8K& config) {
    // Apply unsharp masking for 8K sharpness enhancement
    cv::Mat blurred;
    cv::GaussianBlur(image, blurred, cv::Size(0, 0), 1.5);

    cv::Mat unsharpMask;
    cv::addWeighted(image, 1.5, blurred, -0.5, 0, unsharpMask);

    image = unsharpMask;

    safePrint("8K: Unsharp mask applied", config.verbose);
}

} // namespace Stitching8K
