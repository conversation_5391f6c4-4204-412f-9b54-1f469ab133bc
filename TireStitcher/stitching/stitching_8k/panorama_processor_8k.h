#ifndef PANORAMA_PROCESSOR_8K_H
#define PANORAMA_PROCESSOR_8K_H

#include "config_8k.h"
#include "movement_detection_8k.h"
#include "image_blending_8k.h"
#include "../image_loader.h"
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>
#include <functional>

namespace Stitching8K {

/**
 * 8K-optimized panorama processor with focus on quality and precision.
 * Preserves the working 8K quality improvements with correlation thresholds 0.75/0.92,
 * enhanced blending, and reduced minimum overlap.
 */

/**
 * Main 8K stitching function using optical flow.
 * Optimized for quality with enhanced precision and multi-scale analysis.
 */
cv::Mat stitchTireSurfaceWithOpticalFlow(
    const std::string& inputFolder,
    const std::string& outputFolder,
    const std::string& filePattern,
    bool useBlending,
    int startIdx,
    int maxFrames,
    const StitchConfig8K& config,
    const std::string& serialNumber = "",
    const std::string& subprojectType = ""
);

/**
 * 8K hierarchical stitching function.
 * Advanced hierarchical approach for maximum quality.
 */
cv::Mat stitchTireSurfaceHierarchical(
    const std::string& inputFolder,
    const std::string& outputFolder,
    const std::string& filePattern,
    bool useBlending,
    int startIdx,
    int maxFrames,
    const StitchConfig8K& config,
    const std::string& serialNumber = "",
    const std::string& subprojectType = ""
);

/**
 * 8K-specific image pair stitching.
 * Advanced stitching for two images with quality optimization.
 */
cv::Mat stitchImagePair8K(
    const cv::Mat& img1,
    const cv::Mat& img2,
    bool useBlending,
    const StitchConfig8K& config
);

/**
 * Save intermediate panorama for 8K processing.
 * Enhanced saving with quality preservation.
 */
void saveIntermediatePanorama8K(
    const cv::Mat& panorama,
    const std::string& outputFolder,
    int frameIdx,
    const StitchConfig8K& config
);

/**
 * Save final panorama versions for 8K processing.
 * Creates standard and enhanced versions with 8K-optimized settings.
 */
void savePanoramaVersions8K(
    const cv::Mat& panorama,
    const std::string& outputFolder,
    const StitchConfig8K& config,
    const std::string& serialNumber = "",
    const std::string& subprojectType = ""
);

/**
 * 8K-specific strip extraction.
 * Enhanced strip extraction with quality optimization.
 */
cv::Mat getCentralStrip8K(
    const cv::Mat& image, 
    int stripWidth,
    const StitchConfig8K& config
);

/**
 * Calculate strip width for 8K processing.
 * Advanced calculation based on movement and quality requirements.
 */
int calculateStripWidth8K(
    double movement,
    const StitchConfig8K& config,
    bool isFirstStrip = false
);

/**
 * 8K-specific movement validation.
 * Strict validation with quality thresholds.
 */
bool isValidMovement8K(
    double movement,
    double correlation,
    const StitchConfig8K& config
);

/**
 * Process frame range for 8K stitching.
 * Advanced processing with comprehensive error handling.
 */
cv::Mat processFrameRange8K(
    const std::vector<std::string>& imagePaths,
    int startIdx,
    int endIdx,
    bool useBlending,
    const StitchConfig8K& config
);

/**
 * 8K-specific tile management for large panoramas.
 * Handles memory-efficient processing of large image sequences.
 */
cv::Mat processTiledStitching8K(
    const std::vector<std::string>& imagePaths,
    const std::string& outputFolder,
    bool useBlending,
    const StitchConfig8K& config,
    const std::string& serialNumber = "",
    const std::string& subprojectType = ""
);

/**
 * Advanced quality assessment for 8K panoramas.
 * Evaluates panorama quality and suggests improvements.
 */
double assessPanoramaQuality8K(
    const cv::Mat& panorama,
    const StitchConfig8K& config
);

} // namespace Stitching8K

#endif // PANORAMA_PROCESSOR_8K_H
