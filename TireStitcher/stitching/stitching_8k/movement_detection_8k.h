#ifndef MOVEMENT_DETECTION_8K_H
#define MOVEMENT_DETECTION_8K_H

#include <opencv2/opencv.hpp>
#include <deque>
#include <mutex>
#include "config_8k.h"
#include "../debug_utils.h"

namespace Stitching8K {

/**
 * 8K-optimized movement detection with focus on quality and precision.
 * Preserves the working 8K quality improvements with correlation thresholds 0.75/0.92.
 */

/**
 * Main function for 8K movement detection using optical flow.
 * Optimized for quality with enhanced precision and multi-scale analysis.
 */
double measureMovementWithOpticalFlow(
    const cv::Mat& img1,
    const cv::Mat& img2,
    const StitchConfig8K& config,
    double* outCorrelation = nullptr
);

/**
 * Calculate correlation between two images for 8K processing.
 * Uses comprehensive correlation calculation for quality.
 */
double calculateCorrelation(
    const cv::Mat& img1,
    const cv::Mat& img2,
    const StitchConfig8K& config
);

/**
 * Run template matching optimized for 8K processing.
 * Uses comprehensive template coverage for maximum quality.
 */
void runTemplateMatching(
    const cv::Mat& img1,
    const cv::Mat& img2,
    std::vector<double>& allMovements,
    std::vector<double>& allCorrelations,
    std::vector<double>& allConfidences,
    const StitchConfig8K& config
);

/**
 * 8K-optimized optical flow calculation.
 * Uses multiple scales and enhanced precision for quality.
 */
cv::Mat calculateOpticalFlow8K(
    const cv::Mat& img1,
    const cv::Mat& img2,
    const StitchConfig8K& config
);

/**
 * Extract movement from optical flow for 8K processing.
 * Enhanced extraction with sub-pixel precision.
 */
double extractMovementFromFlow8K(
    const cv::Mat& flow,
    const StitchConfig8K& config,
    bool horizontal = true
);

/**
 * 8K-specific movement validation and filtering.
 * Uses strict thresholds for quality assurance.
 */
bool validateMovement8K(
    double movement,
    double correlation,
    const StitchConfig8K& config
);

/**
 * Calculate weighted average movement for 8K processing.
 * Advanced weighting scheme with confidence analysis.
 */
double calculateWeightedMovement8K(
    const std::vector<double>& movements,
    const std::vector<double>& confidences,
    const StitchConfig8K& config
);

/**
 * 8K-specific multi-resolution movement analysis.
 * Analyzes movement at multiple scales for enhanced accuracy.
 */
double analyzeMultiResolutionMovement8K(
    const cv::Mat& img1,
    const cv::Mat& img2,
    const StitchConfig8K& config
);

/**
 * Enhanced template matching for high correlation scenarios (>= 0.92).
 * Preserves the working 8K quality improvements.
 */
double enhancedTemplateMatching8K(
    const cv::Mat& img1,
    const cv::Mat& img2,
    const StitchConfig8K& config
);

} // namespace Stitching8K

#endif // MOVEMENT_DETECTION_8K_H
