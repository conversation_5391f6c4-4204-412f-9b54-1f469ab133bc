#include "panorama_processor_8k.h"
#include "../debug_utils.h"
#include "../fs_util.h"
#include <chrono>
#include <algorithm>
#include <iomanip>
#include <sstream>
#include <deque>

namespace Stitching8K {

cv::Mat stitchTireSurfaceWithOpticalFlow(
    const std::string& inputFolder,
    const std::string& outputFolder,
    const std::string& filePattern,
    bool useBlending,
    int startIdx,
    int maxFrames,
    const StitchConfig8K& config,
    const std::string& serialNumber,
    const std::string& subprojectType
) {
    auto startTime = std::chrono::high_resolution_clock::now();

    safePrint("Starting 8K panorama stitching with quality-optimized parameters", true);
    safePrint("8K: Using correlation thresholds " + std::to_string(config.correlationThresholdLow) +
              "/" + std::to_string(config.correlationThresholdHigh), true);

    // Create output folder if it doesn't exist
    if (!fs_util::exists(outputFolder)) {
        fs_util::create_directories(outputFolder);
    }

    // Load images
    std::vector<std::string> imagePaths = loadImagePaths(inputFolder, filePattern);
    if (imagePaths.empty()) {
        throw std::runtime_error("No images found in " + inputFolder);
    }

    safePrint("8K: Found " + std::to_string(imagePaths.size()) + " images", config.verbose);

    // Apply frame limits
    if (maxFrames > 0 && maxFrames < static_cast<int>(imagePaths.size())) {
        imagePaths.resize(maxFrames);
    }

    if (startIdx > 0 && startIdx < static_cast<int>(imagePaths.size())) {
        imagePaths.erase(imagePaths.begin(), imagePaths.begin() + startIdx);
    }

    if (imagePaths.size() < 2) {
        throw std::runtime_error("Need at least 2 images for stitching");
    }

    // For large image sets, use tiled processing
    if (imagePaths.size() > 200) {
        safePrint("8K: Using tiled processing for " + std::to_string(imagePaths.size()) + " images", true);
        return processTiledStitching8K(imagePaths, outputFolder, useBlending, config, serialNumber, subprojectType);
    }

    // Load first two images to establish initial parameters
    cv::Mat firstImg = loadImage(imagePaths[0]);
    cv::Mat secondImg = loadImage(imagePaths[1]);

    if (firstImg.empty() || secondImg.empty()) {
        throw std::runtime_error("Failed to load initial images");
    }

    safePrint("8K: Starting with image size: " + std::to_string(firstImg.cols) + "x" + std::to_string(firstImg.rows), config.verbose);

    // Calculate initial movement with 8K precision
    double correlation = 0.0;
    double initialMovement = measureMovementWithOpticalFlow(firstImg, secondImg, config, &correlation);

    safePrint("8K: Initial movement: " + std::to_string(initialMovement) +
              ", correlation: " + std::to_string(correlation), true);

    // Validate initial movement
    if (!isValidMovement8K(initialMovement, correlation, config)) {
        throw std::runtime_error("8K: Initial movement validation failed - correlation: " +
                                std::to_string(correlation) + ", movement: " + std::to_string(initialMovement));
    }

    // Calculate initial strip width with 8K parameters
    int stripWidth = calculateStripWidth8K(initialMovement, config, true);

    // Extract first strip
    cv::Mat firstStrip = getCentralStrip8K(firstImg, stripWidth, config);
    cv::Mat panorama = firstStrip.clone();

    safePrint("8K: Initial strip width: " + std::to_string(stripWidth), config.verbose);

    // Running statistics for 8K quality control
    std::deque<double> recentMovements;
    std::deque<double> recentCorrelations;
    recentMovements.push_back(initialMovement);
    recentCorrelations.push_back(correlation);

    const int MOVING_AVERAGE_SIZE = 7;  // Larger window for 8K stability

    // Statistics tracking
    std::string statsFile = outputFolder + "/stitching_8k_stats.csv";
    std::ofstream stats(statsFile);
    stats << "frame,movement,correlation,strip_width,overlap,quality_score,processing_time_ms" << std::endl;
    stats << "0," << initialMovement << "," << correlation << "," << stripWidth << ",0,1.0,0" << std::endl;

    // Process remaining images with 8K quality optimization
    for (size_t i = 1; i < imagePaths.size(); i++) {
        auto frameStartTime = std::chrono::high_resolution_clock::now();

        safePrint("8K: Processing frame " + std::to_string(i + 1) + "/" + std::to_string(imagePaths.size()), true);

        cv::Mat currentImage = loadImage(imagePaths[i]);
        if (currentImage.empty()) {
            safePrint("8K: Warning - Failed to load image: " + imagePaths[i], true);
            continue;
        }

        // Measure movement with 8K precision
        double frameCorrelation = 0.0;
        double movement = measureMovementWithOpticalFlow(panorama, currentImage, config, &frameCorrelation);

        // Update running statistics
        recentMovements.push_back(movement);
        recentCorrelations.push_back(frameCorrelation);

        if (recentMovements.size() > MOVING_AVERAGE_SIZE) {
            recentMovements.pop_front();
            recentCorrelations.pop_front();
        }

        // Calculate quality-weighted movement for 8K
        double avgCorrelation = std::accumulate(recentCorrelations.begin(), recentCorrelations.end(), 0.0) / recentCorrelations.size();
        double stabilizedMovement = movement;

        // Use correlation-weighted stabilization for 8K quality
        if (frameCorrelation >= config.correlationThresholdHigh && avgCorrelation >= config.correlationThresholdHigh) {
            // High quality scenario - use precise measurement
            stabilizedMovement = movement;
            safePrint("8K: High correlation mode - using precise movement: " + std::to_string(movement), config.verbose);
        } else if (frameCorrelation >= config.correlationThresholdLow) {
            // Medium quality - use weighted average
            double avgMovement = std::accumulate(recentMovements.begin(), recentMovements.end(), 0.0) / recentMovements.size();
            double weight = (frameCorrelation - config.correlationThresholdLow) /
                           (config.correlationThresholdHigh - config.correlationThresholdLow);
            stabilizedMovement = weight * movement + (1.0 - weight) * avgMovement;
            safePrint("8K: Medium correlation mode - weighted movement: " + std::to_string(stabilizedMovement), config.verbose);
        } else {
            safePrint("8K: Low correlation (" + std::to_string(frameCorrelation) + ") - skipping frame", true);
            continue;
        }

        if (!isValidMovement8K(stabilizedMovement, frameCorrelation, config)) {
            safePrint("8K: Movement validation failed - skipping frame", config.verbose);
            continue;
        }

        // Calculate strip width and overlap with 8K parameters
        int currentStripWidth = calculateStripWidth8K(stabilizedMovement, config, false);
        cv::Mat strip = getCentralStrip8K(currentImage, currentStripWidth, config);

        // Calculate optimal overlap with enhanced blending and reduced minimum overlap
        int overlap = calculateOptimalOverlap8K(panorama, strip, frameCorrelation, config);

        // Apply 8K-specific blending
        cv::Mat newPanorama;
        if (useBlending && config.useEnhancedBlending) {
            if (frameCorrelation >= config.correlationThresholdHigh) {
                // Use multi-band blending for highest quality
                newPanorama = multiBandBlend8K(panorama, strip, overlap, config);
            } else {
                // Use enhanced blending
                newPanorama = blendImages(panorama, strip, overlap, config);
            }
        } else {
            // Simple concatenation with quality preservation
            int nonOverlapWidth = strip.cols - overlap;
            newPanorama = cv::Mat(panorama.rows, panorama.cols + nonOverlapWidth, panorama.type());
            panorama.copyTo(newPanorama(cv::Rect(0, 0, panorama.cols, panorama.rows)));
            strip(cv::Rect(overlap, 0, nonOverlapWidth, strip.rows)).copyTo(
                newPanorama(cv::Rect(panorama.cols, 0, nonOverlapWidth, strip.rows))
            );
        }

        panorama = newPanorama;

        // Calculate quality score
        double qualityScore = assessPanoramaQuality8K(panorama, config);

        // Calculate processing time
        auto frameEndTime = std::chrono::high_resolution_clock::now();
        auto frameTime = std::chrono::duration_cast<std::chrono::milliseconds>(frameEndTime - frameStartTime).count();

        // Log statistics
        stats << i << "," << stabilizedMovement << "," << frameCorrelation << ","
              << currentStripWidth << "," << overlap << "," << qualityScore << "," << frameTime << std::endl;

        // Save intermediate results every 25 frames for 8K
        if (i % 25 == 0) {
            saveIntermediatePanorama8K(panorama, outputFolder, static_cast<int>(i), config);
        }

        safePrint("8K: Frame " + std::to_string(i + 1) + " processed - Quality: " +
                  std::to_string(qualityScore), config.verbose);
    }

    stats.close();

    // Save final panorama versions with 8K quality
    savePanoramaVersions8K(panorama, outputFolder, config, serialNumber, subprojectType);

    auto endTime = std::chrono::high_resolution_clock::now();
    auto totalDuration = std::chrono::duration_cast<std::chrono::seconds>(endTime - startTime).count();

    safePrint("8K: Final panorama size: " + std::to_string(panorama.cols) + "x" + std::to_string(panorama.rows), true);
    safePrint("8K: Total processing time: " + std::to_string(totalDuration) + " seconds", true);

    // Final quality assessment
    double finalQuality = assessPanoramaQuality8K(panorama, config);
    safePrint("8K: Final quality score: " + std::to_string(finalQuality), true);

    return panorama;
}

cv::Mat stitchTireSurfaceHierarchical(
    const std::string& inputFolder,
    const std::string& outputFolder,
    const std::string& filePattern,
    bool useBlending,
    int startIdx,
    int maxFrames,
    const StitchConfig8K& config,
    const std::string& serialNumber,
    const std::string& subprojectType
) {
    safePrint("8K: Starting hierarchical stitching for maximum quality", true);

    // Load images
    std::vector<std::string> imagePaths = loadImagePaths(inputFolder, filePattern);
    if (imagePaths.empty()) {
        throw std::runtime_error("No images found in " + inputFolder);
    }

    // Apply limits
    if (maxFrames > 0 && maxFrames < static_cast<int>(imagePaths.size())) {
        imagePaths.resize(maxFrames);
    }

    if (startIdx > 0 && startIdx < static_cast<int>(imagePaths.size())) {
        imagePaths.erase(imagePaths.begin(), imagePaths.begin() + startIdx);
    }

    if (imagePaths.size() < 2) {
        throw std::runtime_error("Need at least 2 images for hierarchical stitching");
    }

    // For small sets, use direct stitching
    if (imagePaths.size() <= 10) {
        return stitchTireSurfaceWithOpticalFlow(inputFolder, outputFolder, filePattern,
                                               useBlending, startIdx, maxFrames, config,
                                               serialNumber, subprojectType);
    }

    // Hierarchical processing for 8K quality
    return processFrameRange8K(imagePaths, 0, static_cast<int>(imagePaths.size()) - 1, useBlending, config);
}

cv::Mat stitchImagePair8K(const cv::Mat& img1, const cv::Mat& img2, bool useBlending, const StitchConfig8K& config) {
    double correlation = 0.0;
    double movement = measureMovementWithOpticalFlow(img1, img2, config, &correlation);

    if (!isValidMovement8K(movement, correlation, config)) {
        throw std::runtime_error("8K: Invalid movement detected in image pair");
    }

    int stripWidth = calculateStripWidth8K(movement, config);
    cv::Mat strip = getCentralStrip8K(img2, stripWidth, config);

    int overlap = calculateOptimalOverlap8K(img1, strip, correlation, config);

    if (useBlending && config.useEnhancedBlending) {
        if (correlation >= config.correlationThresholdHigh) {
            return multiBandBlend8K(img1, strip, overlap, config);
        } else {
            return blendImages(img1, strip, overlap, config);
        }
    } else {
        // Quality concatenation
        int nonOverlapWidth = strip.cols - overlap;
        cv::Mat result(img1.rows, img1.cols + nonOverlapWidth, img1.type());
        img1.copyTo(result(cv::Rect(0, 0, img1.cols, img1.rows)));
        strip(cv::Rect(overlap, 0, nonOverlapWidth, strip.rows)).copyTo(
            result(cv::Rect(img1.cols, 0, nonOverlapWidth, strip.rows))
        );
        return result;
    }
}

void saveIntermediatePanorama8K(const cv::Mat& panorama, const std::string& outputFolder,
                               int frameIdx, const StitchConfig8K& config) {
    if (config.keepTemporaryFiles) {
        std::string filename = "tire_8k_intermediate_" + std::to_string(frameIdx) + ".jpg";
        std::string filepath = outputFolder + "/" + filename;

        std::vector<int> jpegParams = {cv::IMWRITE_JPEG_QUALITY, config.jpegQuality};
        cv::imwrite(filepath, panorama, jpegParams);

        safePrint("8K: Saved intermediate panorama: " + filename, config.verbose);
    }
}

void savePanoramaVersions8K(const cv::Mat& panorama, const std::string& outputFolder,
                           const StitchConfig8K& config, const std::string& serialNumber,
                           const std::string& subprojectType) {
    // Create filename prefix
    std::string filePrefix = "tire_unwrapped";
    if (!serialNumber.empty() && !subprojectType.empty()) {
        std::string safeSerial = serialNumber;
        for (char& c : safeSerial) {
            if (!std::isalnum(c)) {
                c = '_';
            }
        }

        auto now = std::chrono::system_clock::now();
        auto now_time_t = std::chrono::system_clock::to_time_t(now);
        std::tm now_tm = *std::localtime(&now_time_t);
        char timestamp[20];
        std::strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", &now_tm);

        filePrefix = "tire_" + safeSerial + "_" + subprojectType + "_" + timestamp;
    }

    // Save standard version with high quality
    std::string standardPath = outputFolder + "/" + filePrefix + "_full.JPG";
    std::vector<int> jpegParams = {cv::IMWRITE_JPEG_QUALITY, config.jpegQuality};
    cv::imwrite(standardPath, panorama, jpegParams);

    safePrint("8K: Saved standard panorama: " + standardPath, true);

    // Save enhanced version with 8K quality processing
    std::string enhancedPath = outputFolder + "/" + filePrefix + "_enhanced.JPG";
    cv::Mat enhanced = enhancePanorama8K(panorama, enhancedPath, config);

    safePrint("8K: Saved enhanced panorama: " + enhancedPath, true);
}

cv::Mat getCentralStrip8K(const cv::Mat& image, int stripWidth, const StitchConfig8K& config) {
    if (image.empty()) {
        throw std::runtime_error("Input image is empty");
    }

    int height = image.rows;
    int width = image.cols;

    // Validate and adjust strip width for 8K quality
    if (stripWidth <= 0) {
        // Dynamic calculation for 8K
        stripWidth = std::max(50, width / 8);  // Larger default for 8K
        safePrint("8K: Calculated dynamic strip width: " + std::to_string(stripWidth), config.verbose);
    }

    if (stripWidth > width) {
        stripWidth = width;
        safePrint("8K: Strip width capped to image width: " + std::to_string(stripWidth), config.verbose);
    }

    // Calculate central region with sub-pixel precision
    double center = width / 2.0;
    double halfStrip = stripWidth / 2.0;
    int start = std::max(0, static_cast<int>(std::round(center - halfStrip)));
    int end = std::min(width, start + stripWidth);

    return image(cv::Rect(start, 0, end - start, height)).clone();
}

int calculateStripWidth8K(double movement, const StitchConfig8K& config, bool isFirstStrip) {
    if (config.stripWidth > 0) {
        // Use configured strip width for consistency
        return config.stripWidth;
    }

    // Dynamic calculation for 8K quality
    double margin = isFirstStrip ? config.initialStripMarginPercent : config.stripMarginPercent;
    int stripWidth = static_cast<int>(std::round(movement + movement * margin));

    // Apply 8K-specific bounds with quality considerations
    stripWidth = std::max(30, std::min(stripWidth, 200));  // Higher bounds for 8K

    return stripWidth;
}

bool isValidMovement8K(double movement, double correlation, const StitchConfig8K& config) {
    return correlation >= config.correlationThresholdLow &&
           std::abs(movement) > 0.5 &&
           std::abs(movement) < 2000.0;  // 8K-specific validation
}

cv::Mat processFrameRange8K(const std::vector<std::string>& imagePaths, int startIdx, int endIdx,
                           bool useBlending, const StitchConfig8K& config) {
    if (startIdx >= endIdx || startIdx < 0 || endIdx >= static_cast<int>(imagePaths.size())) {
        throw std::runtime_error("Invalid frame range for 8K processing");
    }

    // Base case: single image
    if (startIdx == endIdx) {
        return loadImage(imagePaths[startIdx]);
    }

    // Base case: two images
    if (startIdx + 1 == endIdx) {
        cv::Mat img1 = loadImage(imagePaths[startIdx]);
        cv::Mat img2 = loadImage(imagePaths[endIdx]);
        return stitchImagePair8K(img1, img2, useBlending, config);
    }

    // Recursive case: divide and conquer
    int mid = (startIdx + endIdx) / 2;

    cv::Mat leftPanorama = processFrameRange8K(imagePaths, startIdx, mid, useBlending, config);
    cv::Mat rightPanorama = processFrameRange8K(imagePaths, mid + 1, endIdx, useBlending, config);

    return stitchImagePair8K(leftPanorama, rightPanorama, useBlending, config);
}

cv::Mat processTiledStitching8K(const std::vector<std::string>& imagePaths, const std::string& outputFolder,
                               bool useBlending, const StitchConfig8K& config,
                               const std::string& serialNumber, const std::string& subprojectType) {
    const int TILE_SIZE = 100;  // Process in tiles of 100 images for 8K
    std::vector<cv::Mat> tiles;

    safePrint("8K: Processing " + std::to_string(imagePaths.size()) + " images in tiles of " +
              std::to_string(TILE_SIZE), true);

    for (size_t i = 0; i < imagePaths.size(); i += TILE_SIZE) {
        size_t endIdx = std::min(i + TILE_SIZE, imagePaths.size());

        safePrint("8K: Processing tile " + std::to_string(i / TILE_SIZE + 1) +
                  " (frames " + std::to_string(i) + "-" + std::to_string(endIdx - 1) + ")", true);

        std::vector<std::string> tileImages(imagePaths.begin() + i, imagePaths.begin() + endIdx);
        cv::Mat tile = processFrameRange8K(tileImages, 0, static_cast<int>(tileImages.size()) - 1, useBlending, config);

        if (!tile.empty()) {
            tiles.push_back(tile);

            // Save intermediate tile
            std::string tilePath = outputFolder + "/tile_8k_" + std::to_string(i / TILE_SIZE) + ".jpg";
            std::vector<int> jpegParams = {cv::IMWRITE_JPEG_QUALITY, config.jpegQuality};
            cv::imwrite(tilePath, tile, jpegParams);
        }
    }

    if (tiles.empty()) {
        throw std::runtime_error("8K: No valid tiles created");
    }

    // Stitch tiles together
    safePrint("8K: Stitching " + std::to_string(tiles.size()) + " tiles together", true);

    cv::Mat finalPanorama = tiles[0];
    for (size_t i = 1; i < tiles.size(); i++) {
        finalPanorama = stitchImagePair8K(finalPanorama, tiles[i], useBlending, config);
    }

    return finalPanorama;
}

double assessPanoramaQuality8K(const cv::Mat& panorama, const StitchConfig8K& config) {
    if (panorama.empty()) {
        return 0.0;
    }

    // Simple quality assessment based on image properties
    cv::Mat gray;
    if (panorama.channels() == 3) {
        cv::cvtColor(panorama, gray, cv::COLOR_BGR2GRAY);
    } else {
        gray = panorama;
    }

    // Calculate sharpness (Laplacian variance)
    cv::Mat laplacian;
    cv::Laplacian(gray, laplacian, CV_64F);
    cv::Scalar mean, stddev;
    cv::meanStdDev(laplacian, mean, stddev);
    double sharpness = stddev[0] * stddev[0];

    // Normalize to 0-1 range (empirically determined)
    double qualityScore = std::min(1.0, sharpness / 1000.0);

    return qualityScore;
}

} // namespace Stitching8K
