#include "movement_detection_8k.h"
#include <opencv2/imgproc.hpp>
#include <opencv2/video/tracking.hpp>
#include <numeric>
#include <algorithm>
#include <iomanip>
#include <sstream>
#include <cmath>

namespace Stitching8K {

double calculateCorrelation(const cv::Mat& img1, const cv::Mat& img2, const StitchConfig8K& config) {
    cv::Mat gray1, gray2;
    
    // Convert to grayscale if needed
    if (img1.channels() == 3) {
        cv::cvtColor(img1, gray1, cv::COLOR_BGR2GRAY);
    } else {
        gray1 = img1.clone();
    }
    
    if (img2.channels() == 3) {
        cv::cvtColor(img2, gray2, cv::COLOR_BGR2GRAY);
    } else {
        gray2 = img2.clone();
    }
    
    // Use comprehensive correlation calculation for 8K quality
    int height = gray1.rows;
    int width = gray1.cols;
    
    // Use all templates for maximum quality
    std::vector<double> correlations;
    
    for (double size : config.templateSizes) {
        for (double pos : config.templatePositions) {
            int templateWidth = static_cast<int>(width * size);
            if (templateWidth < 20) continue;  // Higher minimum for 8K quality
            
            int centerPos = static_cast<int>(width * pos);
            int templateStart = std::max(0, centerPos - templateWidth / 2);
            int templateEnd = std::min(width, templateStart + templateWidth);
            
            if (templateEnd - templateStart < 20) continue;
            
            // Extract template
            cv::Mat templ = gray1(cv::Rect(templateStart, 0, templateEnd - templateStart, height));
            
            // Template matching with higher precision
            cv::Mat result;
            cv::matchTemplate(gray2, templ, result, cv::TM_CCOEFF_NORMED);
            
            double minVal, maxVal;
            cv::minMaxLoc(result, &minVal, &maxVal);
            
            // Apply quality threshold
            if (maxVal >= config.templateQualityThreshold) {
                correlations.push_back(maxVal);
            }
        }
    }
    
    if (correlations.empty()) {
        return 0.0;
    }
    
    // Use weighted average for 8K quality (higher correlations get more weight)
    double weightedSum = 0.0;
    double totalWeight = 0.0;
    
    for (double corr : correlations) {
        double weight = corr * corr;  // Square for emphasis on high correlations
        weightedSum += corr * weight;
        totalWeight += weight;
    }
    
    return totalWeight > 0 ? weightedSum / totalWeight : 0.0;
}

void runTemplateMatching(const cv::Mat& img1, const cv::Mat& img2,
                        std::vector<double>& allMovements,
                        std::vector<double>& allCorrelations,
                        std::vector<double>& allConfidences,
                        const StitchConfig8K& config) {
    cv::Mat gray1, gray2;
    
    // Convert to grayscale
    if (img1.channels() == 3) {
        cv::cvtColor(img1, gray1, cv::COLOR_BGR2GRAY);
    } else {
        gray1 = img1.clone();
    }
    
    if (img2.channels() == 3) {
        cv::cvtColor(img2, gray2, cv::COLOR_BGR2GRAY);
    } else {
        gray2 = img2.clone();
    }
    
    int height = gray1.rows;
    int width = gray1.cols;
    
    // Clear output vectors
    allMovements.clear();
    allCorrelations.clear();
    allConfidences.clear();
    
    // Use comprehensive 8K template sizes and positions for maximum quality
    for (size_t s = 0; s < config.templateSizes.size(); s++) {
        for (size_t p = 0; p < config.templatePositions.size(); p++) {
            double size = config.templateSizes[s];
            double pos = config.templatePositions[p];
            
            int templateWidth = static_cast<int>(width * size);
            if (templateWidth < 20) continue;  // Higher minimum for 8K
            
            int centerPos = static_cast<int>(width * pos);
            int templateStart = std::max(0, centerPos - templateWidth / 2);
            int templateEnd = std::min(width, templateStart + templateWidth);
            
            if (templateEnd - templateStart < 20) continue;
            
            // Extract template
            cv::Mat templ = gray1(cv::Rect(templateStart, 0, templateEnd - templateStart, height));
            
            // Template matching with sub-pixel precision
            cv::Mat result;
            cv::matchTemplate(gray2, templ, result, cv::TM_CCOEFF_NORMED);
            
            double minVal, maxVal;
            cv::Point minLoc, maxLoc;
            cv::minMaxLoc(result, &minVal, &maxVal, &minLoc, &maxLoc);
            
            // Sub-pixel refinement for 8K quality
            double subPixelX = maxLoc.x;
            if (maxLoc.x > 0 && maxLoc.x < result.cols - 1) {
                // Parabolic interpolation for sub-pixel accuracy
                double left = result.at<float>(maxLoc.y, maxLoc.x - 1);
                double center = result.at<float>(maxLoc.y, maxLoc.x);
                double right = result.at<float>(maxLoc.y, maxLoc.x + 1);
                
                if (left > 0 && right > 0) {
                    double offset = 0.5 * (left - right) / (left - 2 * center + right);
                    subPixelX = maxLoc.x + offset;
                }
            }
            
            // Calculate movement with sub-pixel precision
            double movement = subPixelX - templateStart;
            
            // Advanced confidence calculation for 8K
            double confidence = maxVal;
            
            // Enhance confidence based on template size and position
            double sizeWeight = 1.0 + (size - 0.1) * 0.5;  // Larger templates get higher weight
            double posWeight = 1.0 - std::abs(pos - 0.5) * 0.2;  // Central positions get higher weight
            confidence *= sizeWeight * posWeight;
            
            // Store results if above threshold
            if (confidence > config.movementConfidenceThreshold) {
                allMovements.push_back(movement);
                allCorrelations.push_back(maxVal);
                allConfidences.push_back(confidence);
            }
        }
    }
}

double calculateWeightedMovement8K(const std::vector<double>& movements,
                                  const std::vector<double>& confidences,
                                  const StitchConfig8K& config) {
    if (movements.empty() || confidences.empty()) {
        return 0.0;
    }
    
    // Advanced weighted average for 8K quality with outlier rejection
    std::vector<std::pair<double, double>> movementConfidencePairs;
    for (size_t i = 0; i < movements.size() && i < confidences.size(); i++) {
        movementConfidencePairs.push_back({movements[i], confidences[i]});
    }
    
    // Sort by confidence (highest first)
    std::sort(movementConfidencePairs.begin(), movementConfidencePairs.end(),
              [](const auto& a, const auto& b) { return a.second > b.second; });
    
    // Use top 70% of measurements for quality
    size_t useCount = std::max(1UL, static_cast<size_t>(movementConfidencePairs.size() * 0.7));
    
    double sumWeightedMoves = 0.0;
    double sumWeights = 0.0;
    
    for (size_t i = 0; i < useCount; i++) {
        double movement = movementConfidencePairs[i].first;
        double confidence = movementConfidencePairs[i].second;
        
        // Use exponential weighting for 8K quality
        double weight = std::exp(confidence - config.movementConfidenceThreshold);
        
        sumWeightedMoves += movement * weight;
        sumWeights += weight;
    }
    
    return sumWeights > 0 ? sumWeightedMoves / sumWeights : 0.0;
}

bool validateMovement8K(double movement, double correlation, const StitchConfig8K& config) {
    // Strict validation for 8K quality
    return correlation >= config.correlationThresholdLow && 
           std::abs(movement) > 0.5 &&  // More sensitive minimum
           std::abs(movement) < 2000.0;  // Higher maximum for 8K
}

double measureMovementWithOpticalFlow(const cv::Mat& img1, const cv::Mat& img2, 
                                     const StitchConfig8K& config,
                                     double* outCorrelation) {
    // Multi-scale optical flow for 8K quality
    cv::Mat scaledImg1, scaledImg2;
    
    // Use smaller scale for higher precision
    cv::resize(img1, scaledImg1, cv::Size(), config.opticalFlowScale, config.opticalFlowScale, cv::INTER_CUBIC);
    cv::resize(img2, scaledImg2, cv::Size(), config.opticalFlowScale, config.opticalFlowScale, cv::INTER_CUBIC);
    
    // Calculate correlation
    double correlation = calculateCorrelation(scaledImg1, scaledImg2, config);
    
    if (outCorrelation != nullptr) {
        *outCorrelation = correlation;
    }
    
    if (config.verbose) {
        safePrint("8K Image correlation: " + std::to_string(correlation), config.verbose);
    }
    
    // Use enhanced template matching for high correlation scenarios (>= 0.92)
    if (correlation >= config.correlationThresholdHigh) {
        double enhancedMovement = enhancedTemplateMatching8K(scaledImg1, scaledImg2, config);
        if (enhancedMovement != 0.0) {
            // Scale back to original resolution
            enhancedMovement /= config.opticalFlowScale;
            
            if (config.verbose) {
                safePrint("8K Enhanced template matching movement: " + std::to_string(enhancedMovement), config.verbose);
            }
            
            return enhancedMovement;
        }
    }
    
    // Use comprehensive template matching
    std::vector<double> allMovements;
    std::vector<double> allCorrelations;
    std::vector<double> allConfidences;
    
    runTemplateMatching(scaledImg1, scaledImg2, allMovements, allCorrelations, allConfidences, config);
    
    if (allMovements.empty()) {
        // Fallback to multi-resolution analysis
        double multiResMovement = analyzeMultiResolutionMovement8K(img1, img2, config);
        if (multiResMovement != 0.0) {
            safePrint("8K: Using multi-resolution movement: " + std::to_string(multiResMovement), config.verbose);
            return multiResMovement;
        }
        
        safePrint("8K: No valid movements detected", config.verbose);
        return 0.0;
    }
    
    // Calculate weighted movement
    double movement = calculateWeightedMovement8K(allMovements, allConfidences, config);
    
    // Scale back to original resolution
    movement /= config.opticalFlowScale;
    
    // Validate movement
    if (!validateMovement8K(movement, correlation, config)) {
        safePrint("8K: Movement validation failed", config.verbose);
        return 0.0;
    }
    
    if (config.verbose) {
        safePrint("8K Final movement: " + std::to_string(movement), config.verbose);
    }
    
    return movement;
}

double analyzeMultiResolutionMovement8K(const cv::Mat& img1, const cv::Mat& img2, const StitchConfig8K& config) {
    std::vector<double> movements;
    
    // Analyze at multiple scales for 8K quality
    for (double scale : config.multiScaleFactors) {
        cv::Mat scaled1, scaled2;
        cv::resize(img1, scaled1, cv::Size(), scale, scale, cv::INTER_CUBIC);
        cv::resize(img2, scaled2, cv::Size(), scale, scale, cv::INTER_CUBIC);
        
        std::vector<double> scaleMovements;
        std::vector<double> scaleCorrelations;
        std::vector<double> scaleConfidences;
        
        runTemplateMatching(scaled1, scaled2, scaleMovements, scaleCorrelations, scaleConfidences, config);
        
        if (!scaleMovements.empty()) {
            double scaleMovement = calculateWeightedMovement8K(scaleMovements, scaleConfidences, config);
            movements.push_back(scaleMovement / scale);  // Scale back
        }
    }
    
    if (movements.empty()) {
        return 0.0;
    }
    
    // Return median for robustness
    std::sort(movements.begin(), movements.end());
    return movements[movements.size() / 2];
}

double enhancedTemplateMatching8K(const cv::Mat& img1, const cv::Mat& img2, const StitchConfig8K& config) {
    // Enhanced template matching for high correlation scenarios (>= 0.92)
    // This preserves the working 8K quality improvements
    
    cv::Mat gray1, gray2;
    
    // Convert to grayscale
    if (img1.channels() == 3) {
        cv::cvtColor(img1, gray1, cv::COLOR_BGR2GRAY);
    } else {
        gray1 = img1.clone();
    }
    
    if (img2.channels() == 3) {
        cv::cvtColor(img2, gray2, cv::COLOR_BGR2GRAY);
    } else {
        gray2 = img2.clone();
    }
    
    // Use larger templates for high-correlation scenarios
    std::vector<double> enhancedSizes = {0.15, 0.2, 0.25, 0.3};
    std::vector<double> enhancedPositions = {0.3, 0.4, 0.5, 0.6, 0.7};
    
    std::vector<double> movements;
    std::vector<double> confidences;
    
    int height = gray1.rows;
    int width = gray1.cols;
    
    for (double size : enhancedSizes) {
        for (double pos : enhancedPositions) {
            int templateWidth = static_cast<int>(width * size);
            if (templateWidth < 30) continue;  // Even higher minimum for enhanced matching
            
            int centerPos = static_cast<int>(width * pos);
            int templateStart = std::max(0, centerPos - templateWidth / 2);
            int templateEnd = std::min(width, templateStart + templateWidth);
            
            if (templateEnd - templateStart < 30) continue;
            
            // Extract template
            cv::Mat templ = gray1(cv::Rect(templateStart, 0, templateEnd - templateStart, height));
            
            // Enhanced template matching
            cv::Mat result;
            cv::matchTemplate(gray2, templ, result, cv::TM_CCOEFF_NORMED);
            
            double minVal, maxVal;
            cv::Point minLoc, maxLoc;
            cv::minMaxLoc(result, &minVal, &maxVal, &minLoc, &maxLoc);
            
            // Only use very high quality matches
            if (maxVal >= 0.95) {
                double movement = maxLoc.x - templateStart;
                movements.push_back(movement);
                confidences.push_back(maxVal);
            }
        }
    }
    
    if (movements.empty()) {
        return 0.0;
    }
    
    return calculateWeightedMovement8K(movements, confidences, config);
}

cv::Mat calculateOpticalFlow8K(const cv::Mat& img1, const cv::Mat& img2, const StitchConfig8K& config) {
    cv::Mat gray1, gray2;
    
    // Convert to grayscale
    if (img1.channels() == 3) {
        cv::cvtColor(img1, gray1, cv::COLOR_BGR2GRAY);
    } else {
        gray1 = img1.clone();
    }
    
    if (img2.channels() == 3) {
        cv::cvtColor(img2, gray2, cv::COLOR_BGR2GRAY);
    } else {
        gray2 = img2.clone();
    }
    
    // Calculate optical flow using enhanced parameters for 8K quality
    cv::Mat flow;
    cv::calcOpticalFlowFarneback(gray1, gray2, flow, 0.5, 5, 21, 5, 7, 1.5, 0);
    
    return flow;
}

double extractMovementFromFlow8K(const cv::Mat& flow, const StitchConfig8K& config, bool horizontal) {
    if (flow.empty()) {
        return 0.0;
    }
    
    // Enhanced flow extraction for 8K quality
    std::vector<double> movements;
    
    // Sample more densely for 8K quality
    for (int y = 0; y < flow.rows; y += 5) {
        for (int x = 0; x < flow.cols; x += 5) {
            cv::Vec2f flowVec = flow.at<cv::Vec2f>(y, x);
            double movement = horizontal ? flowVec[0] : flowVec[1];
            
            // Filter out noise
            if (std::abs(movement) > 0.1 && std::abs(movement) < 100.0) {
                movements.push_back(movement);
            }
        }
    }
    
    if (movements.empty()) {
        return 0.0;
    }
    
    // Use robust statistics for 8K quality
    std::sort(movements.begin(), movements.end());
    
    // Remove outliers (top and bottom 10%)
    size_t removeCount = movements.size() / 10;
    if (removeCount > 0) {
        movements.erase(movements.begin(), movements.begin() + removeCount);
        movements.erase(movements.end() - removeCount, movements.end());
    }
    
    if (movements.empty()) {
        return 0.0;
    }
    
    // Return median for robustness
    return movements[movements.size() / 2];
}

} // namespace Stitching8K
