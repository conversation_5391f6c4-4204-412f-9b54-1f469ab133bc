#include "stitching_factory.h"
#include "../debug_utils.h"
#include "../stitching_4k/panorama_processor_4k.h"
#include "../stitching_8k/panorama_processor_8k.h"
#include <algorithm>
#include <cctype>

std::unique_ptr<BaseStitchConfig> StitchingFactory::createConfig(const std::string& resolutionMode) {
    std::string validatedMode = validateResolutionMode(resolutionMode);

    if (validatedMode == "4K") {
        return std::make_unique<StitchConfig4K>();
    } else {
        return std::make_unique<StitchConfig8K>();
    }
}

cv::Mat StitchingFactory::stitchTireSurface(
    const std::string& inputFolder,
    const std::string& outputFolder,
    const std::string& filePattern,
    bool useBlending,
    int startIdx,
    int maxFrames,
    const std::string& resolutionMode,
    const std::string& serialNumber,
    const std::string& subprojectType
) {
    std::string validatedMode = validateResolutionMode(resolutionMode);

    safePrint("Using " + validatedMode + " stitching pipeline", true);

    if (validatedMode == "4K") {
        StitchConfig4K config;
        config.serialNumber = serialNumber;
        config.subprojectType = subprojectType;

        return Stitching4K::stitchTireSurfaceWithOpticalFlow(
            inputFolder, outputFolder, filePattern, useBlending,
            startIdx, maxFrames, config, serialNumber, subprojectType
        );
    } else {
        StitchConfig8K config;
        config.serialNumber = serialNumber;
        config.subprojectType = subprojectType;

        return Stitching8K::stitchTireSurfaceWithOpticalFlow(
            inputFolder, outputFolder, filePattern, useBlending,
            startIdx, maxFrames, config, serialNumber, subprojectType
        );
    }
}

cv::Mat StitchingFactory::stitchTireSurfaceHierarchical(
    const std::string& inputFolder,
    const std::string& outputFolder,
    const std::string& filePattern,
    bool useBlending,
    int startIdx,
    int maxFrames,
    const std::string& resolutionMode,
    const std::string& serialNumber,
    const std::string& subprojectType
) {
    std::string validatedMode = validateResolutionMode(resolutionMode);

    safePrint("Using " + validatedMode + " hierarchical stitching pipeline", true);

    if (validatedMode == "4K") {
        StitchConfig4K config;
        config.serialNumber = serialNumber;
        config.subprojectType = subprojectType;

        return Stitching4K::stitchTireSurfaceHierarchical(
            inputFolder, outputFolder, filePattern, useBlending,
            startIdx, maxFrames, config, serialNumber, subprojectType
        );
    } else {
        StitchConfig8K config;
        config.serialNumber = serialNumber;
        config.subprojectType = subprojectType;

        return Stitching8K::stitchTireSurfaceHierarchical(
            inputFolder, outputFolder, filePattern, useBlending,
            startIdx, maxFrames, config, serialNumber, subprojectType
        );
    }
}

std::string StitchingFactory::validateResolutionMode(const std::string& mode) {
    std::string upperMode = mode;
    std::transform(upperMode.begin(), upperMode.end(), upperMode.begin(), ::toupper);

    if (upperMode == "4K") {
        return "4K";
    } else if (upperMode == "8K") {
        return "8K";
    } else {
        // Default to 8K for unknown modes
        safePrint("Unknown resolution mode '" + mode + "', defaulting to 8K", true);
        return "8K";
    }
}
